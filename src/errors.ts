// Enhanced error classes
export class ValidationError extends Error {
  constructor(message: string, public field?: string) {
    super(message);
    this.name = "ValidationError";
  }
}

export class ConfigurationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "ConfigurationError";
  }
}

export class GoogleServiceError extends Error {
  constructor(message: string, public service?: string) {
    super(message);
    this.name = "GoogleServiceError";
  }
}

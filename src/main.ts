// Import modules
import { validateConfiguration } from "./config";
import { createDocument } from "./documents";
import { doGet, doPost } from "./handlers";
import type { ProjectProperties } from "./types";

/**
 * The main function calls the testAPI function.
 */
function main(): void {
  testAPI();
}

/**
 * The function `testAPI` validates configuration and creates a test document with specified
 * properties.
 */
function testAPI(): void {
  try {
    // Validate configuration first
    validateConfiguration();

    const createDocResponse = createDocument({
      md: "# Test Document\n\nThis is a test document.",
      properties: {
        name: "Test Document",
        url: "https://docs.google.com/document/d/1234567890",
        category: "PJ概要資料",
        created_at: new Date(),
        updated_at: new Date(),
        created_by: "Taira Urakawa",
        updated_by: "Taira Urakawa",
        related_deps: ["全社", "AI"],
        related_docs: [],
        description: "This is a test document.",
        duration: "1 month",
        status: "未着手",
        assignee: "Taira Urakawa",
      } as ProjectProperties,
    });
    Logger.log(JSON.parse(createDocResponse.getContent()));
  } catch (error) {
    Logger.log(`Test failed: ${error}`);
  }
}

// Expose functions to global scope for testing
(globalThis as any).main = main;
(globalThis as any).doGet = doGet;
(globalThis as any).doPost = doPost;

import { validateConfiguration } from "./config";
import { ValidationError } from "./errors";
import { retrieveMasterDB as getDatabase } from "./database";
import {
  getDocument,
  createDocument,
  overwriteWithMarkdown,
  updateProperties,
} from "./documents";
import {
  validateDocumentId,
  validateMarkdown,
  validateProperties,
} from "./validation";
import { handleError } from "./utils";

/**
 * The function `doGet` processes incoming HTTP requests based on specified actions, handling errors
 * and returning appropriate responses.
 * @param e - The `e` parameter in the `doGet` function is of type `GoogleAppsScript.Events.DoGet`,
 * which represents the event object containing information about the HTTP GET request made to the web
 * app. This object includes parameters, context, and other details related to the request.
 * @returns The `doGet` function returns a `GoogleAppsScript.Content.TextOutput` object. The specific
 * content of the returned object depends on the action parameter provided in the request. If the
 * action is "database", the function will return the result of the `retrieveMasterDB` function. If the
 * action is "document", the function will return the result of the `getDocumentAsMarkdown` function
 * with
 */
export function doGet(
  e: GoogleAppsScript.Events.DoGet
): GoogleAppsScript.Content.TextOutput {
  try {
    validateConfiguration();

    const action: string | undefined = e.parameter.action;
    if (!action) {
      throw new ValidationError("action parameter is required", "action");
    }

    switch (action) {
      case "database":
        return getDatabase();
      case "document":
        const docId: string | undefined = e.parameter.docId;
        if (!docId) {
          throw new ValidationError(
            "docId parameter is required for document action",
            "docId"
          );
        }
        // validateDocumentId(docId);
        return getDocument({ docId: docId });
      default:
        throw new ValidationError(
          `Invalid action: ${action}. Valid actions: database, document`,
          "action"
        );
    }
  } catch (error) {
    return handleError(error);
  }
}

/**
 * The function `doPost` processes incoming HTTP POST requests by validating the request body and
 * performing different actions based on the specified action.
 * @param e - The `e` parameter in the `doPost` function represents the event object that contains
 * information about the HTTP POST request made to the web app. It is of type
 * `GoogleAppsScript.Events.DoPost`. This object includes data such as the request headers, parameters,
 * and body content of the POST
 * @returns The `doPost` function returns a `GoogleAppsScript.Content.TextOutput` object. The specific
 * content of this object will depend on the outcome of the `switch` statement inside the function. The
 * function will return the result of the corresponding action function (e.g., `createDocument`,
 * `overwriteWithMarkdown`, `updateProperties`) if the action is valid and all validations pass. If
 * there is
 */
export function doPost(
  e: GoogleAppsScript.Events.DoPost
): GoogleAppsScript.Content.TextOutput {
  try {
    validateConfiguration();

    if (!e.postData || !e.postData.contents) {
      throw new ValidationError("Request body is required");
    }

    let requestBody: any;
    try {
      requestBody = JSON.parse(e.postData.contents);
    } catch (parseError) {
      throw new ValidationError("Invalid JSON in request body");
    }

    const action: string | undefined = e.parameter.action;
    if (!action) {
      throw new ValidationError("action parameter is required", "action");
    }

    switch (action) {
      case "create_document":
        // validateMarkdown(requestBody.md);
        // validateProperties(requestBody.properties);
        return createDocument(requestBody);
      case "update_document":
        // validateDocumentId(requestBody.docId);
        // validateMarkdown(requestBody.md);
        return overwriteWithMarkdown(requestBody);
      case "update_properties":
        // validateDocumentId(requestBody.docId);
        // validateProperties(requestBody.properties);
        return updateProperties(requestBody);
      default:
        throw new ValidationError(
          `Invalid action: ${action}. Valid actions: create_document, update_document, update_properties`,
          "action"
        );
    }
  } catch (error) {
    return handleError(error);
  }
}

// Type definitions
export type CommonProperties = {
  name: string;
  url: string;
  category: "PJ概要資料" | "タスク";
  created_at: Date;
  updated_at: Date;
  created_by: string;
  updated_by: string;
  related_deps: string[];
  related_docs: string[];
  description: string;
};

export type ProjectProperties = CommonProperties & {
  duration: string;
  status: "未着手" | "進行中" | "完了";
  assignee: string;
};

export type TaskProperties = CommonProperties & {
  due_date: string;
  status: "未着手" | "進行中" | "完了";
  assignee: string;
};

export type Properties = ProjectProperties | TaskProperties;

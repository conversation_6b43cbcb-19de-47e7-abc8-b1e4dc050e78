import { CONFIG } from './config';
import { GoogleServiceError } from './errors';
import { createResponse } from './utils';

/**
 * The function `retrieveMasterDB` retrieves the master database from a Google Sheets spreadsheet
 * and returns it as a JSON response.
 * @returns The function returns a GoogleAppsScript.Content.TextOutput object containing the
 * spreadsheet data in JSON format.
 */
export function retrieveMasterDB(): GoogleAppsScript.Content.TextOutput {
  try {
    const ss = SpreadsheetApp.openById(CONFIG.SPREADSHEET_ID);
    const sheet = ss.getSheetByName("Master");

    if (!sheet) {
      throw new GoogleServiceError("Master sheet not found", "Sheets");
    }

    const data: any[][] | undefined = sheet.getDataRange().getValues();

    if (!data || data.length < 1) {
      return createResponse(true, "Table data retrieved successfully", {
        data: [],
      });
    }

    if (data.length < 2) {
      return createResponse(true, "Table data retrieved successfully", {
        data: [],
      });
    }

    const headers: any[] | undefined = data.shift();
    if (!headers || headers.length === 0) {
      throw new GoogleServiceError(
        "No headers found in Master sheet",
        "Sheets"
      );
    }

    const jsonArray = data
      .map((row, index) => {
        const obj: { [key: string]: any } = {};
        headers.forEach((header, headerIndex) => {
          if (header && header.toString().trim()) {
            obj[header] = row[headerIndex];
          }
        });
        return obj;
      })
      .filter((obj) => Object.keys(obj).length > 0);

    return createResponse(true, "Table data retrieved successfully", {
      data: jsonArray,
      count: jsonArray.length,
    });
  } catch (error) {
    if (error instanceof GoogleServiceError) {
      throw error;
    }
    throw new GoogleServiceError(
      `Failed to retrieve database: ${error}`,
      "Sheets"
    );
  }
}

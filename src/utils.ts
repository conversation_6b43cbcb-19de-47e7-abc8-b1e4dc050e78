import {
  ValidationError,
  ConfigurationError,
  GoogleServiceError,
} from "./errors";

/**
 * The function `handleError` in TypeScript handles different types of errors and logs them for
 * debugging purposes before returning a response.
 * @param {any} error - The `handleError` function is designed to handle different types of errors and
 * return a formatted response based on the type of error encountered. The function first determines
 * the type of error and extracts relevant information from the error object. It then logs the error
 * details for debugging purposes and returns a response object with the
 * @returns The `handleError` function is returning a Google Apps Script `TextOutput` object. The
 * `createResponse` function is being called with parameters `false`, `message`, and an object
 * containing `errorType` and `details`.
 */
export function handleError(error: any): GoogleAppsScript.Content.TextOutput {
  let errorType = "UnknownError";
  let message = "An unknown error occurred";
  let details: any = null;

  if (error instanceof ValidationError) {
    errorType = "ValidationError";
    message = error.message;
    details = { field: error.field };
  } else if (error instanceof ConfigurationError) {
    errorType = "ConfigurationError";
    message = error.message;
  } else if (error instanceof GoogleServiceError) {
    errorType = "GoogleServiceError";
    message = error.message;
    details = { service: error.service };
  } else if (error instanceof Error) {
    message = error.message;
  } else {
    message = String(error);
  }

  // Log error for debugging
  Logger.log(`${errorType}: ${message}`);
  if (details) {
    Logger.log(`Details: ${JSON.stringify(details)}`);
  }

  return createResponse(false, message, {
    errorType: errorType,
    details: details,
  });
}

/**
 * The function `createResponse` generates a JSON response with success status, message, data,
 * timestamp, and version.
 * @param {boolean} success - The `success` parameter is a boolean value indicating whether the
 * operation was successful (`true`) or not (`false`).
 * @param {string} message - The `message` parameter in the `createResponse` function is a string that
 * represents a message or information that you want to include in the response. It can be used to
 * provide details about the outcome of an operation, any errors encountered, or any other relevant
 * information that you want to communicate back to
 * @param {any} data - The `data` parameter in the `createResponse` function is used to pass any
 * additional information or payload that you want to include in the response. This can be any type of
 * data, such as an object, array, string, number, etc. The `data` parameter allows you to customize
 * @returns The `createResponse` function returns a Google Apps Script `TextOutput` object with a JSON
 * response containing the properties `success`, `message`, `data`, `timestamp`, and `version`.
 */
export function createResponse(
  success: boolean,
  message: string,
  data: any
): GoogleAppsScript.Content.TextOutput {
  const response = {
    success: success,
    message: message,
    data: data,
    timestamp: new Date().toISOString(),
  };

  return ContentService.createTextOutput(
    JSON.stringify(response, null, 2)
  ).setMimeType(ContentService.MimeType.JSON);
}

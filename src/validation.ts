import { <PERSON>NFIG } from "./config";
import { ValidationError } from "./errors";
import type { Properties, ProjectProperties, TaskProperties } from "./types";

/**
 * The function `validateMarkdown` in TypeScript validates a given Markdown content to ensure it is not
 * empty, is of type string, and does not exceed a maximum document size specified in the `CONFIG`
 * object.
 * @param {string} md - The `validateMarkdown` function is used to validate a Markdown content string.
 * It checks if the provided Markdown content meets certain criteria before processing it further.
 */
export function validateMarkdown(md: string): void {
  if (!md || typeof md !== "string") {
    throw new ValidationError(
      "Markdown content is required and must be a string",
      "md"
    );
  }
  if (md.trim().length === 0) {
    throw new ValidationError("Markdown content cannot be empty", "md");
  }
  if (new Blob([md]).size > CONFIG.MAX_DOCUMENT_SIZE) {
    throw new ValidationError(
      `Document size exceeds maximum of ${CONFIG.MAX_DOCUMENT_SIZE} bytes`,
      "md"
    );
  }
}

/**
 * The function `validateDocumentId` checks if a given document ID is a valid string containing only
 * alphanumeric characters, underscores, and hyphens.
 * @param {string} docId - The `validateDocumentId` function takes a `docId` parameter of type string.
 * It validates the `docId` to ensure it is not empty, is a string, and contains only alphanumeric
 * characters, underscores, and hyphens. If the `docId` does not meet these criteria
 */
export function validateDocumentId(docId: string): void {
  if (!docId || typeof docId !== "string") {
    throw new ValidationError(
      "Document ID is required and must be a string",
      "docId"
    );
  }
  if (!/^[a-zA-Z0-9_-]+$/.test(docId)) {
    throw new ValidationError(
      "Document ID contains invalid characters",
      "docId"
    );
  }
}

/**
 * The function `validateProperties` in TypeScript validates various properties of an object based on
 * specific criteria and throws validation errors if any criteria are not met.
 * @param {Properties} properties - The `properties` parameter in the `validateProperties` function is
 * an object that contains various fields representing properties of a document. These properties
 * include `name`, `category`, `description`, `related_deps`, `related_docs`, `created_by`,
 * `updated_by`, and additional fields specific to different categories
 */
export function validateProperties(properties: Properties): void {
  if (!properties || typeof properties !== "object") {
    throw new ValidationError("Properties object is required", "properties");
  }

  // Validate name
  if (
    typeof properties.name !== "string" ||
    properties.name.trim().length === 0
  ) {
    throw new ValidationError("Name must be a non-empty string", "name");
  }
  if (properties.name.length > CONFIG.MAX_NAME_LENGTH) {
    throw new ValidationError(
      `Name exceeds maximum length of ${CONFIG.MAX_NAME_LENGTH} characters`,
      "name"
    );
  }

  // Validate category
  if (!CONFIG.VALID_CATEGORIES.includes(properties.category)) {
    throw new ValidationError(
      `Category must be one of: ${CONFIG.VALID_CATEGORIES.join(", ")}`,
      "category"
    );
  }

  // Validate description
  if (typeof properties.description !== "string") {
    throw new ValidationError("Description must be a string", "description");
  }
  if (properties.description.length > CONFIG.MAX_DESCRIPTION_LENGTH) {
    throw new ValidationError(
      `Description exceeds maximum length of ${CONFIG.MAX_DESCRIPTION_LENGTH} characters`,
      "description"
    );
  }

  // Validate arrays
  if (!Array.isArray(properties.related_deps)) {
    throw new ValidationError("related_deps must be an array", "related_deps");
  }
  if (!Array.isArray(properties.related_docs)) {
    throw new ValidationError("related_docs must be an array", "related_docs");
  }

  // Validate user fields
  if (
    typeof properties.created_by !== "string" ||
    properties.created_by.trim().length === 0
  ) {
    throw new ValidationError(
      "created_by must be a non-empty string",
      "created_by"
    );
  }
  if (
    typeof properties.updated_by !== "string" ||
    properties.updated_by.trim().length === 0
  ) {
    throw new ValidationError(
      "updated_by must be a non-empty string",
      "updated_by"
    );
  }

  // Category-specific validation
  if (properties.category === "PJ概要資料") {
    const projectProps = properties as ProjectProperties;
    if (!projectProps.duration || typeof projectProps.duration !== "string") {
      throw new ValidationError(
        "duration is required for project documents",
        "duration"
      );
    }
    if (!CONFIG.VALID_STATUSES.includes(projectProps.status)) {
      throw new ValidationError(
        `status must be one of: ${CONFIG.VALID_STATUSES.join(", ")}`,
        "status"
      );
    }
    if (!projectProps.assignee || typeof projectProps.assignee !== "string") {
      throw new ValidationError(
        "assignee is required for project documents",
        "assignee"
      );
    }
  } else if (properties.category === "タスク") {
    const taskProps = properties as TaskProperties;
    if (!taskProps.due_date || typeof taskProps.due_date !== "string") {
      throw new ValidationError(
        "due_date is required for task documents",
        "due_date"
      );
    }
    // Validate due_date format (basic ISO date check)
    if (!/^\d{4}-\d{2}-\d{2}/.test(taskProps.due_date)) {
      throw new ValidationError(
        "due_date must be in YYYY-MM-DD format",
        "due_date"
      );
    }
    if (!CONFIG.VALID_STATUSES.includes(taskProps.status)) {
      throw new ValidationError(
        `status must be one of: ${CONFIG.VALID_STATUSES.join(", ")}`,
        "status"
      );
    }
    if (!taskProps.assignee || typeof taskProps.assignee !== "string") {
      throw new ValidationError(
        "assignee is required for task documents",
        "assignee"
      );
    }
  }
}

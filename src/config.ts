import { ConfigurationError } from "./errors.js";

// Configuration with validation
export const CONFIG = {
  SPREADSHEET_ID:
    PropertiesService.getScriptProperties().getProperty("SPREADSHEET_ID") ??
    "122UokHhXHUrkwT13OSiR23g-6S3TkpdSzfS6JmO1EMg",
  FOLDER_ID:
    PropertiesService.getScriptProperties().getProperty("FOLDER_ID") ??
    "136jOm1HZeHWK_KvxTVuZ1oywo8b5Oz4q",
  MAX_DOCUMENT_SIZE: 10 * 1024 * 1024, // 10MB
  MAX_NAME_LENGTH: 255,
  MAX_DESCRIPTION_LENGTH: 1000,
  VALID_CATEGORIES: ["PJ概要資料", "タスク"] as const,
  VALID_STATUSES: ["未着手", "進行中", "完了"] as const,
};

/**
 * The function `validateConfiguration` checks if required configuration values are set and tests
 * access to a spreadsheet and folder based on those configurations.
 */
export function validateConfiguration(): void {
  if (!CONFIG.SPREADSHEET_ID) {
    throw new ConfigurationError("SPREADSHEET_ID is not configured");
  }
  if (!CONFIG.FOLDER_ID) {
    throw new ConfigurationError("FOLDER_ID is not configured");
  }

  try {
    // Test spreadsheet access
    const ss = SpreadsheetApp.openById(CONFIG.SPREADSHEET_ID);
    const sheet = ss.getSheetByName("Master");
    if (!sheet) {
      throw new ConfigurationError("Master sheet not found in spreadsheet");
    }
  } catch (error) {
    throw new ConfigurationError(`Cannot access spreadsheet: ${error}`);
  }

  try {
    // Test folder access
    DriveApp.getFolderById(CONFIG.FOLDER_ID);
  } catch (error) {
    throw new ConfigurationError(`Cannot access folder: ${error}`);
  }
}

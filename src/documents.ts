import { CONFIG } from "./config";
import { GoogleServiceError, ValidationError } from "./errors";
import type { Properties, ProjectProperties, TaskProperties } from "./types";
import { createResponse } from "./utils";

/**
 * The function `getDocumentAsMarkdown` retrieves a Google Docs document as Markdown format using its
 * ID.
 * @param params - The `getDocumentAsMarkdown` function takes a single parameter `params` which is an
 * object with a property `docId` of type string. This function is responsible for retrieving a Google
 * Document as Markdown format using the provided `docId`.
 * @returns The function `getDocumentAsMarkdown` returns a `GoogleAppsScript.Content.TextOutput`
 * object. This object contains the content of the document in Markdown format along with the document
 * ID.
 */
export function getDocument(params: {
  docId: string;
}): GoogleAppsScript.Content.TextOutput {
  try {
    const docId: string = params.docId;

    // Check if document exists and is accessible
    try {
      DocumentApp.openById(docId);
    } catch (docError) {
      throw new GoogleServiceError(
        `Document not found or not accessible: ${docId}`,
        "Docs"
      );
    }

    const response = UrlFetchApp.fetch(
      `https://docs.google.com/feeds/download/documents/export/Export?id=${docId}&exportFormat=md`,
      {
        method: "get",
        headers: {
          Authorization: "Bearer " + ScriptApp.getOAuthToken(),
        },
      }
    );

    if (response.getResponseCode() !== 200) {
      throw new GoogleServiceError(
        `Failed to export document. HTTP ${response.getResponseCode()}: ${response.getContentText()}`,
        "Drive"
      );
    }

    const md: string = response.getContentText();

    if (!md) {
      throw new GoogleServiceError(
        "Document export returned empty content",
        "Drive"
      );
    }

    return createResponse(true, "Document retrieved successfully", {
      content: md,
      docId: docId,
    });
  } catch (error) {
    if (error instanceof GoogleServiceError) {
      throw error;
    }
    throw new GoogleServiceError(`Failed to get document: ${error}`, "Docs");
  }
}

/**
 * The function `createDocument` creates a Google Docs document from Markdown content, adds it to a
 * spreadsheet, and returns information about the created document.
 * @param params - The `createDocument` function takes in two parameters:
 * @returns The function `createDocument` is returning a `GoogleAppsScript.Content.TextOutput` object.
 */
export function createDocument(params: {
  md: string;
  properties: Properties;
}): GoogleAppsScript.Content.TextOutput {
  try {
    const md: string = params.md;
    const properties: Properties = params.properties;

    // Create the document
    let docId: string;
    try {
      const file = Drive.Files.create(
        {
          name: properties.name,
          parents: [CONFIG.FOLDER_ID],
          mimeType: MimeType.GOOGLE_DOCS,
        },
        Utilities.newBlob(md, "text/markdown")
      );

      if (!file.id) {
        throw new GoogleServiceError(
          "Failed to create document - no ID returned",
          "Drive"
        );
      }
      docId = file.id;
    } catch (driveError) {
      throw new GoogleServiceError(
        `Failed to create document in Drive: ${driveError}`,
        "Drive"
      );
    }

    let doc: GoogleAppsScript.Document.Document;
    try {
      doc = DocumentApp.openById(docId);
    } catch (docError) {
      // Clean up the created file if we can't open it
      try {
        DriveApp.getFileById(docId).setTrashed(true);
      } catch (cleanupError) {
        Logger.log(`Failed to cleanup document ${docId}: ${cleanupError}`);
      }
      throw new GoogleServiceError(
        `Failed to open created document: ${docError}`,
        "Docs"
      );
    }

    // Add to spreadsheet
    try {
      const ss = SpreadsheetApp.openById(CONFIG.SPREADSHEET_ID);
      const sheet = ss.getSheetByName("Master");

      if (!sheet) {
        throw new GoogleServiceError("Master sheet not found", "Sheets");
      }

      const rowData = [
        properties.name,
        doc.getUrl(),
        properties.category,
        new Date(),
        properties.created_by,
        new Date(),
        properties.created_by,
        properties.related_deps?.join(",") ?? "",
        properties.related_docs?.join(",") ?? "",
        properties.description ?? "",
        properties.category === "PJ概要資料"
          ? (properties as ProjectProperties).duration ?? ""
          : "",
        properties.category === "PJ概要資料"
          ? (properties as ProjectProperties).status ?? ""
          : "",
        properties.category === "PJ概要資料"
          ? (properties as ProjectProperties).assignee ?? ""
          : "",
        properties.category === "タスク"
          ? (properties as TaskProperties).due_date ?? ""
          : "",
        properties.category === "タスク"
          ? (properties as TaskProperties).status ?? ""
          : "",
        properties.category === "タスク"
          ? (properties as TaskProperties).assignee ?? ""
          : "",
      ];

      sheet.appendRow(rowData);
    } catch (sheetError) {
      // Clean up the created document if spreadsheet update fails
      try {
        DriveApp.getFileById(docId).setTrashed(true);
      } catch (cleanupError) {
        Logger.log(`Failed to cleanup document ${docId}: ${cleanupError}`);
      }
      throw new GoogleServiceError(
        `Failed to update Master sheet: ${sheetError}`,
        "Sheets"
      );
    }

    return createResponse(true, "Document created successfully", {
      docId: docId,
      title: doc.getName(),
      url: doc.getUrl(),
    });
  } catch (error) {
    if (error instanceof GoogleServiceError) {
      throw error;
    }
    throw new GoogleServiceError(
      `Failed to create document: ${error}`,
      "Unknown"
    );
  }
}

/**
 * The function `overwriteWithMarkdown` updates a Google Docs document with content from a Markdown
 * string.
 * @param params - The `overwriteWithMarkdown` function takes in two parameters:
 * @returns A GoogleAppsScript.Content.TextOutput object is being returned from the function
 * `overwriteWithMarkdown`.
 */
export function overwriteWithMarkdown(params: {
  docId: string;
  md: string;
}): GoogleAppsScript.Content.TextOutput {
  try {
    const docId: string = params.docId;
    const md: string = params.md;

    // Verify document exists
    let destDoc: GoogleAppsScript.Document.Document;
    try {
      destDoc = DocumentApp.openById(docId);
    } catch (docError) {
      throw new GoogleServiceError(
        `Document not found or not accessible: ${docId}`,
        "Docs"
      );
    }

    // Create temporary document
    let tmpId: string;
    try {
      const tmpFile = Drive.Files.create(
        {
          name: `tmp_${Date.now()}`,
          parents: [CONFIG.FOLDER_ID],
          mimeType: MimeType.GOOGLE_DOCS,
        },
        Utilities.newBlob(md, "text/markdown")
      );

      if (!tmpFile.id) {
        throw new GoogleServiceError(
          "Failed to create temporary document",
          "Drive"
        );
      }
      tmpId = tmpFile.id;
    } catch (driveError) {
      throw new GoogleServiceError(
        `Failed to create temporary document: ${driveError}`,
        "Drive"
      );
    }

    try {
      // Clear destination document
      const destBody = destDoc.getBody();
      destBody.clear();

      // Copy content from temporary document
      const srcDoc = DocumentApp.openById(tmpId);
      const srcBody = srcDoc.getBody();
      let lastDestItem: GoogleAppsScript.Document.ListItem | null = null;

      for (let i = 0; i < srcBody.getNumChildren(); i++) {
        const el = srcBody.getChild(i);

        if (el.getType() === DocumentApp.ElementType.LIST_ITEM) {
          const srcLI = el.asListItem();
          const newLI = destBody.appendListItem(srcLI.getText());

          newLI.setGlyphType(srcLI.getGlyphType());
          newLI.setNestingLevel(srcLI.getNestingLevel());
          newLI.setAttributes(srcLI.getAttributes());

          lastDestItem = newLI;
          continue;
        }

        switch (el.getType()) {
          case DocumentApp.ElementType.TABLE:
            destBody.appendTable(el.copy() as GoogleAppsScript.Document.Table);
            break;
          default:
            destBody.appendParagraph(
              el.copy() as GoogleAppsScript.Document.Paragraph
            );
        }
        lastDestItem = null;
      }
    } catch (updateError) {
      throw new GoogleServiceError(
        `Failed to update document content: ${updateError}`,
        "Docs"
      );
    } finally {
      // Always clean up temporary file
      try {
        DriveApp.getFileById(tmpId).setTrashed(true);
      } catch (cleanupError) {
        Logger.log(
          `Failed to cleanup temporary document ${tmpId}: ${cleanupError}`
        );
      }
    }

    return createResponse(true, "Document updated successfully", {
      docId: docId,
      title: destDoc.getName(),
    });
  } catch (error) {
    if (error instanceof GoogleServiceError) {
      throw error;
    }
    throw new GoogleServiceError(
      `Failed to update document: ${error}`,
      "Unknown"
    );
  }
}

/**
 * The function `updateProperties` updates the properties of a document in Google Sheets based on the
 * provided parameters.
 * @param params - The `updateProperties` function takes in two parameters:
 * @returns The `updateProperties` function returns a `GoogleAppsScript.Content.TextOutput` object.
 * This object contains information about the success or failure of updating the properties, along with
 * details such as the document ID and the updated name.
 */
export function updateProperties(params: {
  docId: string;
  properties: Properties;
}): GoogleAppsScript.Content.TextOutput {
  try {
    const docId: string = params.docId;
    const properties: Properties = params.properties;

    // Verify document exists
    try {
      DocumentApp.openById(docId);
    } catch (docError) {
      throw new GoogleServiceError(
        `Document not found or not accessible: ${docId}`,
        "Docs"
      );
    }

    const ss = SpreadsheetApp.openById(CONFIG.SPREADSHEET_ID);
    const sheet = ss.getSheetByName("Master");

    if (!sheet) {
      throw new GoogleServiceError("Master sheet not found", "Sheets");
    }

    const dataRange = sheet.getDataRange();
    const rows = dataRange.getValues();

    if (rows.length < 2) {
      throw new GoogleServiceError("No data found in Master sheet", "Sheets");
    }

    // Find row by document ID in URL column
    const rowIndex = rows.findIndex(
      (row) => row[1] && row[1].toString().includes(docId)
    );

    if (rowIndex === -1) {
      throw new ValidationError(
        `Document not found in Master DB: ${docId}`,
        "docId"
      );
    }

    const oldRow = rows[rowIndex];
    if (!oldRow) {
      throw new GoogleServiceError(
        "Failed to retrieve existing row data",
        "Sheets"
      );
    }

    // Construct updated row
    const updatedRow = [
      properties.name ?? oldRow[0],
      oldRow[1], // URL remains unchanged
      oldRow[2], // category remains unchanged
      oldRow[3], // created_at remains unchanged
      oldRow[4], // created_by remains unchanged
      new Date(), // updated_at
      properties.updated_by,
      properties.related_deps?.join(",") ?? oldRow[7],
      properties.related_docs?.join(",") ?? oldRow[8],
      properties.description ?? oldRow[9],
      properties.category === "PJ概要資料"
        ? (properties as ProjectProperties).duration ?? oldRow[10]
        : oldRow[10],
      properties.category === "PJ概要資料"
        ? (properties as ProjectProperties).status ?? oldRow[11]
        : oldRow[11],
      properties.category === "PJ概要資料"
        ? (properties as ProjectProperties).assignee ?? oldRow[12]
        : oldRow[12],
      properties.category === "タスク"
        ? (properties as TaskProperties).due_date ?? oldRow[13]
        : oldRow[13],
      properties.category === "タスク"
        ? (properties as TaskProperties).status ?? oldRow[14]
        : oldRow[14],
      properties.category === "タスク"
        ? (properties as TaskProperties).assignee ?? oldRow[15]
        : oldRow[15],
    ];

    try {
      sheet
        .getRange(rowIndex + 1, 1, 1, updatedRow.length)
        .setValues([updatedRow]);
    } catch (sheetError) {
      throw new GoogleServiceError(
        `Failed to update spreadsheet: ${sheetError}`,
        "Sheets"
      );
    }

    // Update document name in Drive
    try {
      DriveApp.getFileById(docId).setName(properties.name);
    } catch (driveError) {
      Logger.log(
        `Warning: Failed to update document name in Drive: ${driveError}`
      );
      // Don't throw error here as the main update succeeded
    }

    return createResponse(true, "Properties updated successfully", {
      docId: docId,
      name: properties.name,
    });
  } catch (error) {
    if (error instanceof GoogleServiceError) {
      throw error;
    }
    throw new GoogleServiceError(
      `Failed to update properties: ${error}`,
      "Unknown"
    );
  }
}

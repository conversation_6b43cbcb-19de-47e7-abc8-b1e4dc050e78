{"name": "docs-db", "module": "src/main.ts", "type": "module", "private": true, "devDependencies": {"@types/bun": "latest"}, "peerDependencies": {"typescript": "^5"}, "dependencies": {"@types/google-apps-script": "^1.0.97"}, "scripts": {"build": "bun build ./src/main.ts --outdir ./dist --target 'browser'", "push": "bun run build && clasp push", "watch": "bun build ./src/main.ts --outdir ./dist --target 'browser' --watch"}}